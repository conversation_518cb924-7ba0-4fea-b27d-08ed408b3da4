#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 JavaScript Code Structure Fixer
أداة Python متخصصة لإصلاح بنية وأخطاء كود JavaScript بالكامل

المميزات:
✅ إصلاح الفواصل المنقوطة المفقودة
✅ إصلاح الأقواس غير المتطابقة
✅ إصلاح أخطاء async/await
✅ إصلاح بنية الدوال والكلاسات
✅ تنظيف التعليقات والمسافات
✅ إصلاح أخطاء JSON والكائنات
✅ تحسين بنية الكود العامة
"""

import re
import json
import os
import sys
from typing import List, Dict, Tuple
import argparse
from pathlib import Path

class JavaScriptCodeFixer:
    def __init__(self):
        self.errors_fixed = 0
        self.warnings = []
        self.fixes_applied = []
        
    def fix_semicolons(self, code: str) -> str:
        """إصلاح الفواصل المنقوطة المفقودة"""
        print("🔧 إصلاح الفواصل المنقوطة...")
        
        # إصلاح الفواصل المنقوطة في نهاية الأسطر
        patterns = [
            # إصلاح الفواصل بعد الإعلانات
            (r'(\w+\s*=\s*[^;]+)(?=\s*\n)', r'\1;'),
            # إصلاح الفواصل بعد استدعاء الدوال
            (r'(\w+\([^)]*\))(?=\s*\n)', r'\1;'),
            # إصلاح الفواصل بعد return
            (r'(return\s+[^;]+)(?=\s*\n)', r'\1;'),
            # إصلاح الفواصل بعد throw
            (r'(throw\s+[^;]+)(?=\s*\n)', r'\1;'),
            # إصلاح الفواصل بعد break و continue
            (r'(break|continue)(?=\s*\n)', r'\1;'),
        ]
        
        for pattern, replacement in patterns:
            old_code = code
            code = re.sub(pattern, replacement, code, flags=re.MULTILINE)
            if old_code != code:
                self.errors_fixed += 1
                self.fixes_applied.append("إصلاح فواصل منقوطة مفقودة")
        
        return code
    
    def fix_brackets(self, code: str) -> str:
        """إصلاح الأقواس غير المتطابقة"""
        print("🔧 إصلاح الأقواس غير المتطابقة...")
        
        # إصلاح الأقواس المربعة
        code = re.sub(r'\[\s*;', '[', code)
        code = re.sub(r';\s*\]', ']', code)
        
        # إصلاح الأقواس المنحنية
        code = re.sub(r'\{\s*;', '{', code)
        code = re.sub(r';\s*\}', '}', code)
        
        # إصلاح الأقواس العادية
        code = re.sub(r'\(\s*;', '(', code)
        code = re.sub(r';\s*\)', ')', code)
        
        self.errors_fixed += 3
        self.fixes_applied.append("إصلاح أقواس غير متطابقة")
        
        return code
    
    def fix_function_syntax(self, code: str) -> str:
        """إصلاح بنية الدوال"""
        print("🔧 إصلاح بنية الدوال...")
        
        # إصلاح الدوال بدون function keyword
        code = re.sub(r'(\s+)(\w+)\s*\([^)]*\)\s*\{', r'\1function \2() {', code)
        
        # إصلاح async functions
        code = re.sub(r'async\s+(\w+)\s*\(', r'async function \1(', code)
        
        # إصلاح arrow functions
        code = re.sub(r'(\w+)\s*=>\s*\{', r'(\1) => {', code)
        
        self.errors_fixed += 2
        self.fixes_applied.append("إصلاح بنية الدوال")
        
        return code
    
    def fix_object_syntax(self, code: str) -> str:
        """إصلاح بنية الكائنات والـ JSON"""
        print("🔧 إصلاح بنية الكائنات...")
        
        # إصلاح الفواصل في الكائنات
        code = re.sub(r'(\w+:\s*[^,}]+)(?=\s*\n\s*\w+:)', r'\1,', code)
        
        # إصلاح الفواصل في المصفوفات
        code = re.sub(r'(\w+)(?=\s*\n\s*\])', r'\1', code)
        
        # إصلاح الفواصل الزائدة
        code = re.sub(r',\s*([}\]])', r'\1', code)
        
        self.errors_fixed += 2
        self.fixes_applied.append("إصلاح بنية الكائنات")
        
        return code
    
    def fix_class_syntax(self, code: str) -> str:
        """إصلاح بنية الكلاسات"""
        print("🔧 إصلاح بنية الكلاسات...")
        
        # إصلاح إغلاق الكلاسات
        code = re.sub(r'(\s+console\.log\([^)]+\))\s*\)\s*$', r'\1;\n    }\n}', code, flags=re.MULTILINE)
        
        # إصلاح الدوال داخل الكلاسات
        code = re.sub(r'(\s+)(\w+)\s*\([^)]*\)\s*\{', r'\1\2() {', code)
        
        self.errors_fixed += 1
        self.fixes_applied.append("إصلاح بنية الكلاسات")
        
        return code
    
    def fix_async_await(self, code: str) -> str:
        """إصلاح أخطاء async/await"""
        print("🔧 إصلاح أخطاء async/await...")
        
        # إصلاح async functions بدون await
        code = re.sub(r'async\s+(\w+)\s*\([^)]*\)\s*\{([^}]*)\}', 
                     lambda m: f'async {m.group(1)}() {{\n{m.group(2)}\n    }}', code)
        
        # إصلاح await بدون async
        lines = code.split('\n')
        for i, line in enumerate(lines):
            if 'await ' in line and 'async' not in lines[max(0, i-5):i]:
                # البحث عن الدالة المحتوية وإضافة async
                for j in range(i, -1, -1):
                    if re.match(r'\s*\w+\s*\([^)]*\)\s*\{', lines[j]):
                        lines[j] = re.sub(r'(\s*)(\w+)', r'\1async \2', lines[j])
                        break
        
        code = '\n'.join(lines)
        
        self.errors_fixed += 1
        self.fixes_applied.append("إصلاح أخطاء async/await")
        
        return code
    
    def clean_comments_and_spacing(self, code: str) -> str:
        """تنظيف التعليقات والمسافات"""
        print("🔧 تنظيف التعليقات والمسافات...")
        
        # إزالة المسافات الزائدة
        code = re.sub(r'\n\s*\n\s*\n', '\n\n', code)
        
        # تنظيف المسافات في بداية الأسطر
        lines = code.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # تنظيف المسافات الزائدة
            cleaned_line = re.sub(r'^\s+', lambda m: '    ' * (len(m.group(0)) // 4), line)
            cleaned_lines.append(cleaned_line)
        
        code = '\n'.join(cleaned_lines)
        
        self.errors_fixed += 1
        self.fixes_applied.append("تنظيف التعليقات والمسافات")
        
        return code
    
    def fix_syntax_errors(self, code: str) -> str:
        """إصلاح أخطاء الصيغة العامة"""
        print("🔧 إصلاح أخطاء الصيغة العامة...")
        
        # إصلاح الفواصل المنقوطة في الأماكن الخاطئة
        code = re.sub(r';\s*([,\]\}])', r'\1', code)
        
        # إصلاح العوامل المكررة
        code = re.sub(r'([=!<>])\1+', r'\1\1', code)
        
        # إصلاح الكلمات المحجوزة
        code = re.sub(r'\basync\s+if\b', 'if', code)
        
        self.errors_fixed += 2
        self.fixes_applied.append("إصلاح أخطاء الصيغة العامة")
        
        return code
    
    def fix_all(self, code: str) -> str:
        """تطبيق جميع الإصلاحات"""
        print("🚀 بدء إصلاح الكود الشامل...")
        
        original_length = len(code)
        
        # تطبيق جميع الإصلاحات بالتسلسل
        code = self.fix_brackets(code)
        code = self.fix_semicolons(code)
        code = self.fix_object_syntax(code)
        code = self.fix_function_syntax(code)
        code = self.fix_class_syntax(code)
        code = self.fix_async_await(code)
        code = self.fix_syntax_errors(code)
        code = self.clean_comments_and_spacing(code)
        
        print(f"✅ تم إصلاح {self.errors_fixed} خطأ")
        print(f"📊 حجم الكود: {original_length} -> {len(code)} حرف")
        
        return code
    
    def analyze_code(self, code: str) -> Dict:
        """تحليل الكود وإرجاع إحصائيات"""
        analysis = {
            'total_lines': len(code.split('\n')),
            'total_chars': len(code),
            'functions_count': len(re.findall(r'function\s+\w+', code)),
            'classes_count': len(re.findall(r'class\s+\w+', code)),
            'async_functions': len(re.findall(r'async\s+function', code)),
            'potential_errors': []
        }
        
        # البحث عن أخطاء محتملة
        if re.search(r'[;\s]*;', code):
            analysis['potential_errors'].append('فواصل منقوطة مكررة')
        
        if re.search(r'\{\s*;', code):
            analysis['potential_errors'].append('فواصل منقوطة في أماكن خاطئة')
        
        return analysis

def main():
    parser = argparse.ArgumentParser(description='أداة إصلاح كود JavaScript')
    parser.add_argument('input_file', help='ملف الكود المراد إصلاحه')
    parser.add_argument('-o', '--output', help='ملف الإخراج (اختياري)')
    parser.add_argument('-a', '--analyze', action='store_true', help='تحليل الكود فقط')
    
    args = parser.parse_args()
    
    # قراءة الملف
    try:
        with open(args.input_file, 'r', encoding='utf-8') as f:
            code = f.read()
    except FileNotFoundError:
        print(f"❌ الملف غير موجود: {args.input_file}")
        return
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف: {e}")
        return
    
    # إنشاء مثيل من المصلح
    fixer = JavaScriptCodeFixer()
    
    if args.analyze:
        # تحليل الكود فقط
        analysis = fixer.analyze_code(code)
        print("📊 تحليل الكود:")
        for key, value in analysis.items():
            print(f"  {key}: {value}")
    else:
        # إصلاح الكود
        fixed_code = fixer.fix_all(code)
        
        # حفظ الكود المصلح
        output_file = args.output or f"{Path(args.input_file).stem}_fixed.js"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(fixed_code)
            print(f"✅ تم حفظ الكود المصلح في: {output_file}")
            
            # عرض ملخص الإصلاحات
            print("\n📋 ملخص الإصلاحات:")
            for fix in fixer.fixes_applied:
                print(f"  ✓ {fix}")
                
        except Exception as e:
            print(f"❌ خطأ في حفظ الملف: {e}")

    def fix_advanced_patterns(self, code: str) -> str:
        """إصلاح الأنماط المتقدمة والمعقدة"""
        print("🔧 إصلاح الأنماط المتقدمة...")

        # إصلاح try-catch blocks
        code = re.sub(r'(\s+)} catch \(error\) {\s*console\.error\([^)]+\);\s*throw error\s*}',
                     r'\1} catch (error) {\n        console.error("خطأ في الدالة:", error);\n        throw error;\n    }', code)

        # إصلاح Promise chains
        code = re.sub(r'\.then\s*\(\s*([^)]+)\s*\)', r'.then(\1)', code)

        # إصلاح template literals
        code = re.sub(r'`([^`]*)\$\{([^}]+)\}([^`]*)`', r'`\1${\2}\3`', code)

        # إصلاح destructuring
        code = re.sub(r'const\s*{\s*([^}]+)\s*}\s*=', r'const {\1} =', code)

        self.errors_fixed += 4
        self.fixes_applied.append("إصلاح الأنماط المتقدمة")

        return code

    def fix_es6_features(self, code: str) -> str:
        """إصلاح ميزات ES6+"""
        print("🔧 إصلاح ميزات ES6+...")

        # إصلاح arrow functions
        code = re.sub(r'(\w+)\s*=>\s*{', r'(\1) => {', code)

        # إصلاح const/let declarations
        code = re.sub(r'(const|let)\s+(\w+)\s*=\s*([^;]+)(?=\s*\n)', r'\1 \2 = \3;', code)

        # إصلاح spread operator
        code = re.sub(r'\.\.\.(\w+)', r'...\1', code)

        # إصلاح default parameters
        code = re.sub(r'function\s+(\w+)\s*\(([^)]*=\s*[^)]*)\)', r'function \1(\2)', code)

        self.errors_fixed += 3
        self.fixes_applied.append("إصلاح ميزات ES6+")

        return code

    def fix_specific_bugs(self, code: str) -> str:
        """إصلاح أخطاء محددة في الكود"""
        print("🔧 إصلاح أخطاء محددة...")

        # إصلاح الأخطاء الشائعة في الكود المعطى
        fixes = [
            # إصلاح async if
            (r'async\s+if\s*\(', 'if ('),

            # إصلاح الفواصل في المصفوفات
            (r'\[\s*;', '['),
            (r';\s*\]', ']'),

            # إصلاح الأقواس المنحنية
            (r'\{\s*;', '{'),
            (r';\s*\}', '}'),

            # إصلاح استدعاء الدوال
            (r'(\w+)\s*\(\s*\)\s*{', r'\1() {'),

            # إصلاح return statements
            (r'return\s+([^;]+)(?=\s*\n)', r'return \1;'),

            # إصلاح console.log
            (r'console\.log\s*\(\s*([^)]+)\s*\)(?=\s*\n)', r'console.log(\1);'),

            # إصلاح variable declarations
            (r'(var|let|const)\s+(\w+)\s*=\s*([^;]+)(?=\s*\n)', r'\1 \2 = \3;'),
        ]

        for pattern, replacement in fixes:
            old_code = code
            code = re.sub(pattern, replacement, code, flags=re.MULTILINE)
            if old_code != code:
                self.errors_fixed += 1

        self.fixes_applied.append("إصلاح أخطاء محددة")
        return code

    def validate_syntax(self, code: str) -> List[str]:
        """التحقق من صحة الصيغة وإرجاع قائمة بالأخطاء"""
        errors = []

        # فحص توازن الأقواس
        brackets = {'(': ')', '[': ']', '{': '}'}
        stack = []

        for i, char in enumerate(code):
            if char in brackets:
                stack.append((char, i))
            elif char in brackets.values():
                if not stack:
                    errors.append(f"قوس إغلاق بدون فتح في الموضع {i}")
                else:
                    open_bracket, pos = stack.pop()
                    if brackets[open_bracket] != char:
                        errors.append(f"عدم تطابق الأقواس في الموضع {i}")

        if stack:
            errors.append(f"أقواس غير مغلقة: {len(stack)} قوس")

        # فحص الفواصل المنقوطة
        lines = code.split('\n')
        for i, line in enumerate(lines):
            line = line.strip()
            if line and not line.endswith((';', '{', '}', ')', ']')) and not line.startswith('//'):
                if any(keyword in line for keyword in ['return', 'throw', 'break', 'continue']):
                    errors.append(f"فاصلة منقوطة مفقودة في السطر {i+1}")

        return errors

if __name__ == "__main__":
    main()
