Write-Host "Testing Python Service endpoints..."

# Test health endpoint
try {
    Write-Host "`n1. Testing /health endpoint:"
    $health = Invoke-RestMethod -Uri "http://localhost:8000/health" -Method GET
    Write-Host "✅ Health check successful"
    $health | ConvertTo-Json -Depth 2
} catch {
    Write-Host "❌ Health check failed: $($_.Exception.Message)"
}

# Test capture endpoint
try {
    Write-Host "`n2. Testing /capture endpoint:"
    $captureBody = @{
        url = "https://httpbin.org/html"
        report_id = "test_capture"
        width = 1920
        height = 1080
        wait_time = 3
    } | ConvertTo-Json
    
    $capture = Invoke-RestMethod -Uri "http://localhost:8000/capture" -Method POST -ContentType "application/json" -Body $captureBody
    Write-Host "✅ Capture endpoint successful"
    Write-Host "Success: $($capture.success)"
} catch {
    Write-Host "❌ Capture endpoint failed: $($_.Exception.Message)"
}

# Test v4_website endpoint
try {
    Write-Host "`n3. Testing /v4_website endpoint:"
    $v4Body = @{
        url = "https://httpbin.org/html"
        screenshot_id = "test_v4"
        report_id = "test_report"
    } | ConvertTo-Json
    
    $v4 = Invoke-RestMethod -Uri "http://localhost:8000/v4_website" -Method POST -ContentType "application/json" -Body $v4Body
    Write-Host "✅ v4_website endpoint successful"
    Write-Host "Success: $($v4.success)"
    Write-Host "File path: $($v4.file_path)"
} catch {
    Write-Host "❌ v4_website endpoint failed: $($_.Exception.Message)"
    Write-Host "Status code: $($_.Exception.Response.StatusCode)"
}

Write-Host "`nEndpoint testing completed."
