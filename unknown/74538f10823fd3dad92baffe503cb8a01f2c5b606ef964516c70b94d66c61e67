$body = @{
    url = "https://httpbin.org/html"
    screenshot_id = "test_v4_screenshot"
    report_id = "test_report_123"
} | ConvertTo-Json

try {
    Write-Host "Testing v4_website endpoint..."
    Write-Host "Request body: $body"
    
    $response = Invoke-RestMethod -Uri "http://localhost:8000/v4_website" -Method POST -ContentType "application/json" -Body $body
    
    Write-Host "SUCCESS! Response:"
    $response | ConvertTo-Json -Depth 3
    
    if ($response.success) {
        Write-Host "✅ Screenshot captured successfully!"
        Write-Host "File path: $($response.file_path)"
        Write-Host "Screenshot ID: $($response.screenshot_id)"
        Write-Host "Report ID: $($response.report_id)"
    } else {
        Write-Host "❌ Screenshot capture failed: $($response.error)"
    }
    
} catch {
    Write-Host "❌ ERROR: $($_.Exception.Message)"
    Write-Host "Response: $($_.Exception.Response)"
}
