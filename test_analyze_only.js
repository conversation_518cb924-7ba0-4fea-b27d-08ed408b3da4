    // تحليل صفحة واحدة
    async analyzePage(url, htmlContent, headers) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // جمع النماذج
        const forms = Array.from(doc.querySelectorAll('form')).map(form => ({
            action: form.action || '',
            method: form.method || 'GET',
            inputs: Array.from(form.querySelectorAll('input, textarea, select')).map(input => ({
                name: input.name || '',
                type: input.type || 'text',
                value: input.value || '',
                required: input.required || false,
                placeholder: input.placeholder || ''
            }))
        }));

        // جمع الروابط
        const links = Array.from(doc.querySelectorAll('a[href]')).map(link => link.href);
        const domain = new URL(url).hostname;
        const internalLinks = links.filter(link => link.includes(domain));
        const externalLinks = links.filter(link => !link.includes(domain) && link.startsWith('http'));

        // جمع السكربتات
        const scripts = Array.from(doc.querySelectorAll('script[src]')).map(script => script.src);
        const inlineScripts = Array.from(doc.querySelectorAll('script:not([src])')).length;

        // جمع الصور
        const images = Array.from(doc.querySelectorAll('img[src]')).map(img => img.src);

        // اكتشاف التقنيات
        const technologies = [];
        
        // فحص وجود jQuery
        if (scripts.some(script => script.includes('jquery'))) {
            technologies.push('jQuery');
        }
        
        // فحص وجود React
        if (scripts.some(script => script.includes('react'))) {
            technologies.push('React');
        }
        
        // فحص وجود Angular
        if (scripts.some(script => script.includes('angular'))) {
            technologies.push('Angular');
        }
        
        // فحص وجود Bootstrap
        if (scripts.some(script => script.includes('bootstrap'))) {
            technologies.push('Bootstrap');
        }

        return {
            url: url,
            title: doc.title || 'Untitled',
            status_code: 200,
            headers: headers,
            forms: forms,
            links: {
                internal: internalLinks,
                external: externalLinks,
                total: links.length
            },
            scripts: {
                external: scripts,
                inline: inlineScripts,
                total: scripts.length + inlineScripts
            },
            images: images,
            technologies: technologies,
            content_length: htmlContent.length,
            analysis_timestamp: new Date().toISOString()
        };
    }