/**
 * جسر الربط بين النظام v4 ونظام التقاط الصور بـ Python
 * يوفر واجهة سهلة لاستخدام خدمة Python من JavaScript
 */

class PythonScreenshotBridge {
    constructor() {
        this.version = '4.0';
        this.serviceName = 'Python Screenshot Bridge v4.0';
        this.pythonScriptPath = './assets/modules/bugbounty/screenshot_service.py';
        this.isAvailable = false;
        this.stats = {
            total_calls: 0,
            successful_calls: 0,
            failed_calls: 0
        };
        
        console.log(`🐍 ${this.serviceName} تم تحميله بنجاح`);
        this.checkPythonAvailability();
    }
    
    // فحص توفر Python
    async checkPythonAvailability() {
        try {
            // في بيئة المتصفح، نحتاج لاستخدام API أو WebSocket
            // هنا سنحاكي الفحص
            this.isAvailable = typeof window !== 'undefined' && window.electronAPI;
            
            if (this.isAvailable) {
                console.log('✅ Python Screenshot Service متوفر');
            } else {
                console.log('⚠️ Python Screenshot Service غير متوفر - سيتم استخدام النظام البديل');
            }
            
        } catch (error) {
            console.error('❌ خطأ في فحص توفر Python:', error);
            this.isAvailable = false;
        }
    }
    
    // تشغيل أمر Python
    async executePythonCommand(command, args = []) {
        try {
            this.stats.total_calls++;
            
            // في بيئة Electron
            if (window.electronAPI && window.electronAPI.runPython) {
                const result = await window.electronAPI.runPython(this.pythonScriptPath, [command, ...args]);
                
                if (result.success) {
                    this.stats.successful_calls++;
                    return JSON.parse(result.output);
                } else {
                    throw new Error(result.error);
                }
            }
            
            // في بيئة Node.js
            if (typeof require !== 'undefined') {
                const { spawn } = require('child_process');
                
                return new Promise((resolve, reject) => {
                    const python = spawn('python', [this.pythonScriptPath, command, ...args]);
                    let output = '';
                    let error = '';
                    
                    python.stdout.on('data', (data) => {
                        output += data.toString();
                    });
                    
                    python.stderr.on('data', (data) => {
                        error += data.toString();
                    });
                    
                    python.on('close', (code) => {
                        if (code === 0) {
                            try {
                                const result = JSON.parse(output);
                                this.stats.successful_calls++;
                                resolve(result);
                            } catch (parseError) {
                                this.stats.failed_calls++;
                                reject(new Error(`خطأ في تحليل النتيجة: ${parseError.message}`));
                            }
                        } else {
                            this.stats.failed_calls++;
                            reject(new Error(`Python script failed: ${error}`));
                        }
                    });
                });
            }
            
            // إذا لم تكن أي من البيئات متوفرة
            throw new Error('Python execution environment not available');
            
        } catch (error) {
            this.stats.failed_calls++;
            console.error('❌ خطأ في تشغيل Python:', error);
            throw error;
        }
    }
    
    // التقاط صورة واحدة
    async captureWebsiteScreenshot(url, reportId = null) {
        try {
            console.log(`📸 التقاط صورة Python: ${url}`);
            
            const args = [url];
            if (reportId) args.push(reportId);
            
            const result = await this.executePythonCommand('single', args);
            
            if (result && result.success) {
                console.log('✅ تم التقاط الصورة بـ Python بنجاح');
                return result;
            } else {
                throw new Error('فشل في التقاط الصورة');
            }
            
        } catch (error) {
            console.error('❌ خطأ في التقاط الصورة بـ Python:', error);
            return null;
        }
    }
    
    // التقاط صور الثغرة
    async captureVulnerabilityScreenshots(url, vulnerabilityName, reportId) {
        try {
            console.log(`🎯 التقاط صور الثغرة بـ Python: ${vulnerabilityName}`);
            
            const result = await this.executePythonCommand('vulnerability', [url, vulnerabilityName, reportId]);
            
            if (result) {
                console.log('✅ تم التقاط صور الثغرة بـ Python بنجاح');
                return result;
            } else {
                throw new Error('فشل في التقاط صور الثغرة');
            }
            
        } catch (error) {
            console.error('❌ خطأ في التقاط صور الثغرة بـ Python:', error);
            return null;
        }
    }
    
    // التقاط صور قبل وبعد للنظام v4
    async captureBeforeAfterScreenshots(url, reportId, vulnerabilityName = null) {
        try {
            console.log(`📷 التقاط صور قبل/بعد بـ Python: ${url}`);
            
            const args = [url, reportId];
            if (vulnerabilityName) args.push(vulnerabilityName);
            
            const result = await this.executePythonCommand('v4_before_after', args);
            
            if (result && result.success) {
                console.log('✅ تم التقاط صور قبل/بعد بـ Python بنجاح');
                return result;
            } else {
                throw new Error('فشل في التقاط صور قبل/بعد');
            }
            
        } catch (error) {
            console.error('❌ خطأ في التقاط صور قبل/بعد بـ Python:', error);
            return null;
        }
    }
    
    // التقاط صورة للنظام v4
    async captureForV4System(url, screenshotId, reportId = null) {
        try {
            console.log(`🎯 التقاط صورة للنظام v4 بـ Python: ${screenshotId}`);
            
            const args = [url, screenshotId];
            if (reportId) args.push(reportId);
            
            const result = await this.executePythonCommand('v4_website', args);
            
            if (result && result.success) {
                console.log('✅ تم التقاط صورة للنظام v4 بـ Python بنجاح');
                return result;
            } else {
                throw new Error('فشل في التقاط صورة للنظام v4');
            }
            
        } catch (error) {
            console.error('❌ خطأ في التقاط صورة للنظام v4 بـ Python:', error);
            return null;
        }
    }
    
    // تثبيت المتطلبات
    async installDependencies() {
        try {
            console.log('📦 تثبيت متطلبات Python...');
            
            const result = await this.executePythonCommand('install', []);
            
            if (result && result.success) {
                console.log('✅ تم تثبيت متطلبات Python بنجاح');
                return true;
            } else {
                throw new Error('فشل في تثبيت المتطلبات');
            }
            
        } catch (error) {
            console.error('❌ خطأ في تثبيت متطلبات Python:', error);
            return false;
        }
    }
    
    // الحصول على الإحصائيات
    async getStats() {
        try {
            const pythonStats = await this.executePythonCommand('stats', []);
            
            return {
                bridge_stats: this.stats,
                python_stats: pythonStats,
                is_available: this.isAvailable,
                service_name: this.serviceName,
                version: this.version
            };
            
        } catch (error) {
            console.error('❌ خطأ في الحصول على الإحصائيات:', error);
            return {
                bridge_stats: this.stats,
                python_stats: null,
                is_available: this.isAvailable,
                error: error.message
            };
        }
    }
    
    // فحص الحالة
    async checkHealth() {
        try {
            const stats = await this.getStats();
            return {
                healthy: this.isAvailable && stats.python_stats !== null,
                stats: stats,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                healthy: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// إنشاء instance عام
window.PythonScreenshotBridge = PythonScreenshotBridge;

// تصدير للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PythonScreenshotBridge;
}

console.log('🐍 Python Screenshot Bridge v4.0 جاهز للاستخدام');
