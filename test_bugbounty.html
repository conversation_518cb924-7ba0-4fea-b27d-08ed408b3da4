<!DOCTYPE html>
<html>
<head>
    <title>اختبار Bug Bounty System</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>اختبار Bug Bounty System</h1>
    <div id="output"></div>
    
    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    
    <script>
        console.log('🔍 اختبار تحميل Bug Bounty System...');
        
        // تحقق من تحميل الكلاس
        if (typeof BugBountyCore !== 'undefined') {
            console.log('✅ BugBountyCore محمل بنجاح');
            document.getElementById('output').innerHTML += '<p>✅ BugBountyCore محمل بنجاح</p>';
            
            // إنشاء instance
            try {
                const bugBountyInstance = new BugBountyCore();
                console.log('✅ تم إنشاء instance بنجاح');
                document.getElementById('output').innerHTML += '<p>✅ تم إنشاء instance بنجاح</p>';
                
                // اختبار دالة أساسية
                if (typeof bugBountyInstance.generateProfessionalAnalysis === 'function') {
                    console.log('✅ دالة generateProfessionalAnalysis موجودة');
                    document.getElementById('output').innerHTML += '<p>✅ دالة generateProfessionalAnalysis موجودة</p>';
                } else {
                    console.log('❌ دالة generateProfessionalAnalysis مفقودة');
                    document.getElementById('output').innerHTML += '<p>❌ دالة generateProfessionalAnalysis مفقودة</p>';
                }
                
                // اختبار دالة loadPromptTemplate
                if (typeof bugBountyInstance.loadPromptTemplate === 'function') {
                    console.log('✅ دالة loadPromptTemplate موجودة');
                    document.getElementById('output').innerHTML += '<p>✅ دالة loadPromptTemplate موجودة</p>';
                } else {
                    console.log('❌ دالة loadPromptTemplate مفقودة');
                    document.getElementById('output').innerHTML += '<p>❌ دالة loadPromptTemplate مفقودة</p>';
                }
                
            } catch (error) {
                console.error('❌ خطأ في إنشاء instance:', error);
                document.getElementById('output').innerHTML += '<p>❌ خطأ في إنشاء instance: ' + error.message + '</p>';
            }
            
        } else {
            console.log('❌ BugBountyCore غير محمل');
            document.getElementById('output').innerHTML += '<p>❌ BugBountyCore غير محمل</p>';
        }
        
        // اختبار تحميل النظام في index.html
        console.log('🔍 اختبار النظام في index.html...');
        
        // محاولة تحميل index.html في iframe
        const iframe = document.createElement('iframe');
        iframe.src = 'index.html';
        iframe.style.width = '100%';
        iframe.style.height = '400px';
        iframe.onload = function() {
            console.log('✅ تم تحميل index.html');
            
            // تحقق من النظام في iframe
            try {
                const iframeWindow = iframe.contentWindow;
                if (iframeWindow.BugBountyCore) {
                    console.log('✅ BugBountyCore موجود في index.html');
                    document.getElementById('output').innerHTML += '<p>✅ BugBountyCore موجود في index.html</p>';
                } else {
                    console.log('❌ BugBountyCore غير موجود في index.html');
                    document.getElementById('output').innerHTML += '<p>❌ BugBountyCore غير موجود في index.html</p>';
                }
                
                if (iframeWindow.startBugBountyScan) {
                    console.log('✅ startBugBountyScan موجودة في index.html');
                    document.getElementById('output').innerHTML += '<p>✅ startBugBountyScan موجودة في index.html</p>';
                } else {
                    console.log('❌ startBugBountyScan غير موجودة في index.html');
                    document.getElementById('output').innerHTML += '<p>❌ startBugBountyScan غير موجودة في index.html</p>';
                }
                
            } catch (error) {
                console.error('❌ خطأ في الوصول لـ iframe:', error);
                document.getElementById('output').innerHTML += '<p>❌ خطأ في الوصول لـ iframe: ' + error.message + '</p>';
            }
        };
        
        document.body.appendChild(iframe);
    </script>
</body>
</html>
